
import { v4 as uuidv4 } from 'uuid';

enum TipoUnidad {
  DESPACHO = 'DESPACHO',
  SECRETARIA = 'SECRETARIA',
  AUXILIAR = 'AUXILIAR'
}

enum EstadoSeccion {
  ACTIVA = 'ACTIVA',
  INACTIVA = 'INACTIVA'
}

enum TipoRelacionUsuarioSeccion {
  JEFE = 'JEFE',
  ASISTENTE = 'ASISTENTE',
  CONSULTA = 'CONSULTA'
  // Add other relation types as needed
}

class CodigoSeccion {
  private value: string;

  constructor(codigo: string) {
    this.validarFormato(codigo);
    this.value = codigo;
  }

  private validarFormato(codigo: string): void {
    // Validation logic for codigo format
    if (!codigo || !/^\d{2}\.\d{2}\.\d{2}$/.test(codigo)) {
      throw new Error("El código debe tener el formato XX.XX.XX");
    }
  }

  getValue(): string {
    return this.value;
  }

  identificarTipoUnidad(): TipoUnidad {
    const segmentos = this.value.split('.');

    if (segmentos[1] === '00' && segmentos[2] === '00') {
      return TipoUnidad.DESPACHO;
    } else if (segmentos[2] === '00') {
      return TipoUnidad.SECRETARIA;
    } else {
      return TipoUnidad.AUXILIAR;
    }
  }
}

class UsuarioSeccion {
  private username: string;
  private relacion: TipoRelacionUsuarioSeccion;
  private seccion: Seccion;

  constructor(username: string, relacion: TipoRelacionUsuarioSeccion, seccion: Seccion) {
    this.username = username;
    this.relacion = relacion;
    this.seccion = seccion;
  }

  getUsername(): string {
    return this.username;
  }

  getRelacion(): TipoRelacionUsuarioSeccion {
    return this.relacion;
  }

  getSeccion(): Seccion {
    return this.seccion;
  }
}

class SeccionSinResponsableException extends Error {
  constructor() {
    super("La sección no puede ser activada sin un responsable");
    this.name = "SeccionSinResponsableException";
  }
}

class UsuarioSeccionRelacionDobleException extends Error {
  constructor() {
    super("El usuario ya existe con otra relación en la sección");
    this.name = "UsuarioSeccionRelacionDobleException";
  }
}

class UsuarioSeccionDuplicadoException extends Error {
  constructor() {
    super("El usuario ya existe en la sección");
    this.name = "UsuarioSeccionDuplicadoException";
  }
}

class SeccionYaInactivaException extends Error {
  constructor() {
    super("La sección ya se encuentra inactiva");
    this.name = "SeccionYaInactivaException";
  }
}

export class Seccion {
  private id: string;
  private codigo: CodigoSeccion;
  private nombre: string;
  private responsable: string | null;
  private tipo: TipoUnidad;
  private estado: EstadoSeccion;
  private padre: Seccion | null;
  private hijos: Set<Seccion>;
  private usuarios: Set<UsuarioSeccion>;

  constructor(codigo: string, nombre: string) {
    if (codigo == null || nombre == null) {
      throw new Error("Código y nombre son requeridos");
    }
    this.id = uuidv4();
    this.codigo = new CodigoSeccion(codigo);
    this.tipo = this.codigo.identificarTipoUnidad();
    this.nombre = nombre;
    this.estado = EstadoSeccion.ACTIVA;
    this.responsable = null;
    this.padre = null;
    this.hijos = new Set<Seccion>();
    this.usuarios = new Set<UsuarioSeccion>();
  }

  // Getters
  getId(): string {
    return this.id;
  }

  getNombre(): string {
    return this.nombre;
  }

  getResponsable(): string | null {
    return this.responsable;
  }

  getTipo(): TipoUnidad {
    return this.tipo;
  }

  getEstado(): EstadoSeccion {
    return this.estado;
  }

  getPadre(): Seccion | null {
    return this.padre;
  }

  getHijos(): Set<Seccion> {
    return this.hijos;
  }

  getUsuarios(): Set<UsuarioSeccion> {
    return this.usuarios;
  }

  // Setters
  setId(id: string): void {
    this.id = id;
  }

  setNombre(nombre: string): void {
    this.nombre = nombre;
  }

  setResponsable(responsable: string): void {
    this.responsable = responsable;
  }

  setEstado(estado: EstadoSeccion): void {
    this.estado = estado;
  }

  // Methods

  getCodigo(): string {
    return this.codigo.getValue();
  }

  codigoSuperiorInmediato(): string | null {
    return this.padre ? this.padre.getCodigo() : null;
  }

  nombreSuperiorInmediato(): string | null {
    return this.padre ? this.padre.getNombre() : null;
  }

  esUnDespacho(): boolean {
    return this.tipo === TipoUnidad.DESPACHO;
  }

  setPadre(padre: Seccion | null): void {
    this.validarPadre(padre);
    const antiguoPadre = this.padre;
    if (padre) this.validarJerarquiaCodigo(padre, this);
    this.padre = padre;

    if (antiguoPadre) {
      antiguoPadre.hijos.delete(this);
    }

    if (padre && !padre.hijos.has(this)) {
      padre.hijos.add(this);
    }
  }

  private validarPadre(padre: Seccion | null): void {
    if (!padre) return;

    if (this.esUnDespacho() && !padre.esUnDespacho()) {
      throw new Error("No se puede asignar un padre a una seccion despacho");
    }
    if (padre === this) {
      throw new Error("No se puede autoagregar una seccion como padre");
    }
    if (padre.hijos.has(this)) {
      throw new Error("No se puede agregar un padre a una seccion hijo");
    }
  }

  agregarHijo(subSeccion: Seccion): void {
    this.validarHijo(subSeccion);
    if (subSeccion.padre !== this) {
      subSeccion.setPadre(this);
    }
  }

  private validarHijo(subSeccion: Seccion): void {
    if (!subSeccion || subSeccion === this) {
      throw new Error("No se puede autoagregar una seccion como hijo");
    }

    // Verificar si la sección que intentamos agregar como hijo es nuestro padre
    if (this.padre === subSeccion) {
      throw new Error("No se puede agregar como hijo a una sección que ya es padre");
    }

    // Verificar si ya es hijo (ignorar si ya existe)
    if (subSeccion.padre === this) {
      return;
    }

    // Verificar la jerarquía de tipos
    if (this.tieneCategoriaInferior(this.tipo, subSeccion.tipo)) {
      throw new Error("No se puede agregar un hijo con categoría superior");
    }

    this.validarJerarquiaCodigo(this, subSeccion);
  }

  private tieneCategoriaInferior(tipoActual: TipoUnidad, tipoComparar: TipoUnidad): boolean {
    if (tipoActual === TipoUnidad.AUXILIAR) return true;
    if (tipoActual === TipoUnidad.SECRETARIA && tipoComparar === TipoUnidad.DESPACHO) return true;
    return false;
  }

  private validarJerarquiaCodigo(padre: Seccion | null, hijo: Seccion): void {
    if (!padre || !padre.getPadre()) return;
    const codigoPadre = padre.getCodigo();
    const codigoHijo = hijo.getCodigo();

    const segmentosPadre = codigoPadre.split('.');
    const segmentosHijo = codigoHijo.split('.');

    // El primer segmento debe coincidir para mantener la jerarquía
    if (segmentosPadre[0] !== segmentosHijo[0]) {
      throw new Error("El código del hijo debe pertenecer a la misma jerarquía del padre");
    }

    // Para secretarías, el segundo segmento debe ser coherente
    if (padre.getTipo() === TipoUnidad.DESPACHO && segmentosHijo[1] !== "00") {
      if (segmentosPadre[0] !== segmentosHijo[0]) {
        throw new Error("El código de la secretaría debe pertenecer al mismo despacho");
      }
    }

    // Para auxiliares, los dos primeros segmentos deben coincidir con su secretaría padre
    if (padre.getTipo() === TipoUnidad.SECRETARIA && segmentosHijo[2] !== "00") {
      if (segmentosPadre[0] !== segmentosHijo[0] ||
          segmentosPadre[1] !== segmentosHijo[1]) {
        throw new Error("El código del auxiliar debe pertenecer a la misma secretaría");
      }
    }
  }

  removerHijo(hijo: Seccion): void {
    if (!this.hijos.has(hijo)) {
      throw new Error("No se puede remover un hijo que no existe");
    }
    this.hijos.delete(hijo);
  }

  agregarUsuario(username: string, relacion: TipoRelacionUsuarioSeccion): void {
    if (username == null || relacion == null) {
      throw new Error("El username y la relación no pueden ser nulos");
    }

    const usuarioExistente = Array.from(this.usuarios)
      .find(u => u.getUsername() === username);

    if (usuarioExistente) {
      if (usuarioExistente.getRelacion() !== relacion) {
        throw new UsuarioSeccionRelacionDobleException();
      }
      throw new UsuarioSeccionDuplicadoException();
    }

    const nuevoUsuario = new UsuarioSeccion(username, relacion, this);
    this.usuarios.add(nuevoUsuario);
  }

  removerUsuario(username: string): void {
    if (username == null) {
      throw new Error("El username no puede ser nulo");
    }

    const usuarioAEliminar = Array.from(this.usuarios)
      .find(u => u.getUsername() === username);

    if (!usuarioAEliminar) {
      throw new Error("El usuario no existe en la sección");
    }

    this.usuarios.delete(usuarioAEliminar);
  }

  validarActivacion(): void {
    if (this.responsable == null || this.responsable.trim() === '') {
      throw new SeccionSinResponsableException();
    }
  }

  validarActivacionPermitida(): void {
    this.validarActivacion();
  }

  validarInactivacionPermitida(): void {
    if (this.estado === EstadoSeccion.INACTIVA) {
      throw new SeccionYaInactivaException();
    }
  }

  activar(): void {
    this.validarActivacionPermitida();
    this.estado = EstadoSeccion.ACTIVA;
  }

  inactivar(): void {
    this.validarInactivacionPermitida();
    this.estado = EstadoSeccion.INACTIVA;
  }

  asignarEstadoPorDefecto(): void {
    this.estado = (this.responsable == null || this.responsable.trim() === '')
      ? EstadoSeccion.INACTIVA
      : this.estado;
  }
}

export { TipoUnidad, EstadoSeccion, TipoRelacionUsuarioSeccion, UsuarioSeccion };
}